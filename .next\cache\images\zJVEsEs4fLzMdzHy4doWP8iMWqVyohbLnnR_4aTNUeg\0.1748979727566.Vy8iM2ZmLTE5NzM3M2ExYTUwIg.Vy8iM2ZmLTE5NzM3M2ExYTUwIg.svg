
<svg width="300" height="400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="shirtGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff"/>
      <stop offset="100%" style="stop-color:#f8f8f8"/>
    </linearGradient>
  </defs>
  <rect width="300" height="400" fill="#fafafa"/>
  <!-- Shirt silhouette -->
  <path d="M80 60 L220 60 L240 80 L240 200 L220 220 L80 220 L60 200 L60 80 Z" fill="url(#shirtGrad)" stroke="#e0e0e0" stroke-width="1"/>
  <!-- Collar -->
  <path d="M120 60 L180 60 L170 80 L130 80 Z" fill="#f0f0f0" stroke="#d0d0d0" stroke-width="1"/>
  <!-- Buttons -->
  <circle cx="150" cy="100" r="3" fill="#e0e0e0"/>
  <circle cx="150" cy="130" r="3" fill="#e0e0e0"/>
  <circle cx="150" cy="160" r="3" fill="#e0e0e0"/>
  <!-- Sleeves -->
  <ellipse cx="60" cy="120" rx="20" ry="40" fill="url(#shirtGrad)" stroke="#e0e0e0" stroke-width="1"/>
  <ellipse cx="240" cy="120" rx="20" ry="40" fill="url(#shirtGrad)" stroke="#e0e0e0" stroke-width="1"/>
</svg>
