"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/product/[id]/page",{

/***/ "(app-pages-browser)/./app/product/[id]/page.tsx":
/*!***********************************!*\
  !*** ./app/product/[id]/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_HeartIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=HeartIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/HeartIcon.js\");\n/* harmony import */ var _barrel_optimize_names_HeartIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=HeartIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/HeartIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst product = {\n    id: 1,\n    name: 'Moon oversized cotton-poplin shirt',\n    brand: 'The Row',\n    price: '£1,200',\n    description: 'An elegant statement of The Row\\'s exacting, tailoring, this cream Moon shirt is cut from crisp cotton-poplin to an oversized fit with clean-lined details. Shown here with The Row Enzo straight-leg leather trousers, The Row Celine calf-hair-lined leather loafers and The Row Celine calf-hair-lined leather loafers.',\n    details: [\n        'Oversized fit',\n        'Button-front closure',\n        'Spread collar',\n        'Long sleeves with button cuffs',\n        'Curved hem',\n        'Made in Italy'\n    ],\n    sizeGuide: 'Size Guide',\n    deliveryReturns: 'Delivery and Returns',\n    images: [\n        '/products/the-row-shirt-1.jpg',\n        '/products/the-row-shirt-2.jpg',\n        '/products/the-row-shirt-3.jpg',\n        '/products/the-row-shirt-4.jpg',\n        '/products/the-row-shirt-5.jpg',\n        '/products/the-row-shirt-6.jpg'\n    ],\n    sizes: [\n        'XS',\n        'S',\n        'M',\n        'L',\n        'XL'\n    ],\n    colors: [\n        {\n            name: 'Cream',\n            value: '#F5F5DC'\n        },\n        {\n            name: 'White',\n            value: '#FFFFFF'\n        },\n        {\n            name: 'Black',\n            value: '#000000'\n        }\n    ]\n};\nconst recentlyViewed = [\n    {\n        id: 2,\n        name: 'Moon oversized cotton-poplin shirt',\n        brand: 'The Row',\n        price: '£1,200',\n        image: '/products/the-row-shirt-recent.jpg'\n    }\n];\nfunction ProductPage() {\n    _s();\n    const [selectedSize, setSelectedSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedImage, setSelectedImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isWishlisted, setIsWishlisted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const nextImage = ()=>{\n        setSelectedImage((prev)=>(prev + 1) % productData.images.length);\n    };\n    const prevImage = ()=>{\n        setSelectedImage((prev)=>(prev - 1 + productData.images.length) % productData.images.length);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"text-sm text-gray-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: \"/\",\n                            className: \"hover:text-black\",\n                            children: \"Home\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"mx-2\",\n                            children: \"/\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: \"/women\",\n                            className: \"hover:text-black\",\n                            children: \"Women\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"mx-2\",\n                            children: \"/\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-black\",\n                            children: productData.name\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 pb-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"lg:grid lg:grid-cols-2 lg:gap-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:sticky lg:top-24 lg:h-fit\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"aspect-[4/5] relative bg-gray-50 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            src: productData.images[selectedImage],\n                                            alt: productData.name,\n                                            fill: true,\n                                            className: \"object-cover\",\n                                            priority: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: prevImage,\n                                            className: \"absolute left-4 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white p-3 rounded-full shadow-lg transition-all\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChevronLeftIcon, {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: nextImage,\n                                            className: \"absolute right-4 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white p-3 rounded-full shadow-lg transition-all\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChevronRightIcon, {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden lg:grid lg:grid-cols-4 gap-2 mb-4\",\n                                    children: productData.images.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setSelectedImage(index),\n                                            className: \"aspect-square relative bg-gray-50 border-2 transition-all \".concat(selectedImage === index ? 'border-black' : 'border-transparent hover:border-gray-300'),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                src: image,\n                                                alt: \"\".concat(productData.name, \" view \").concat(index + 1),\n                                                fill: true,\n                                                className: \"object-cover\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center gap-2 py-4 lg:hidden\",\n                                    children: productData.images.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setSelectedImage(index),\n                                            className: \"w-2 h-2 rounded-full transition-all \".concat(selectedImage === index ? 'bg-black' : 'bg-gray-300')\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:py-0 py-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-500 mb-2 uppercase tracking-wide\",\n                                    children: productData.brand\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl lg:text-3xl font-light mb-4 text-black\",\n                                    children: productData.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-semibold mb-8 text-black\",\n                                    children: productData.price\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Select size\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    href: \"#\",\n                                                    className: \"text-sm text-gray-600 underline hover:text-black\",\n                                                    children: \"Size Guide\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hidden lg:grid lg:grid-cols-5 gap-2 mb-4\",\n                                            children: productData.sizes.map((size)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setSelectedSize(size),\n                                                    className: \"py-3 px-4 border text-sm font-medium transition-all \".concat(selectedSize === size ? 'border-black bg-black text-white' : 'border-gray-300 hover:border-black'),\n                                                    children: size\n                                                }, size, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: selectedSize,\n                                            onChange: (e)=>setSelectedSize(e.target.value),\n                                            className: \"w-full p-3 border border-gray-300 text-sm lg:hidden focus:outline-none focus:border-black\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select size\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 17\n                                                }, this),\n                                                productData.sizes.map((size)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: size,\n                                                        children: size\n                                                    }, size, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4 mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            disabled: !selectedSize,\n                                            className: \"w-full py-4 font-medium text-base transition-all \".concat(selectedSize ? 'bg-black text-white hover:bg-gray-800' : 'bg-gray-200 text-gray-400 cursor-not-allowed'),\n                                            children: selectedSize ? 'Add to bag' : 'Select a size'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsWishlisted(!isWishlisted),\n                                            className: \"w-full border-2 border-black py-4 flex items-center justify-center gap-2 font-medium hover:bg-black hover:text-white transition-all\",\n                                            children: [\n                                                isWishlisted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HeartIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"h-5 w-5 text-red-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 19\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HeartIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 19\n                                                }, this),\n                                                isWishlisted ? 'Remove from wishlist' : 'Add to wishlist'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold mb-4 text-lg\",\n                                                    children: \"Description\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 leading-relaxed\",\n                                                    children: productData.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold mb-4 text-lg\",\n                                                    children: \"Details\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-2\",\n                                                    children: productData.details.map((detail, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"text-gray-600 flex items-start\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"w-2 h-2 bg-black rounded-full mt-2 mr-3 flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 224,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                detail\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 223,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold mb-4 text-lg\",\n                                                    children: \"Size and fit\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"The model is 180cm tall and wears a size M\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold mb-4 text-lg\",\n                                                    children: \"Care Instructions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: productData.care\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t border-gray-200 pt-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"flex items-center justify-between w-full py-4 text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold text-lg\",\n                                                        children: \"Delivery and Returns\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChevronRightIcon, {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"max-w-7xl mx-auto px-4 py-16 border-t border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-2xl font-light mb-8 text-center\",\n                        children: \"You might also like\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 lg:grid-cols-4 gap-6\",\n                        children: [\n                            1,\n                            2,\n                            3,\n                            4\n                        ].map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/product/\".concat(item),\n                                className: \"group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"aspect-[3/4] relative bg-gray-50 mb-3 overflow-hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            src: productData.images[(item - 1) % productData.images.length],\n                                            alt: \"Related product \".concat(item),\n                                            fill: true,\n                                            className: \"object-cover group-hover:scale-105 transition-transform duration-300\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500 mb-1 uppercase tracking-wide\",\n                                        children: productData.brand\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-black mb-2 line-clamp-2\",\n                                        children: [\n                                            \"Similar \",\n                                            productData.name\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-semibold\",\n                                        children: productData.price\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, item, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 258,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 256,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductPage, \"8mtIn3UDqgEjmayVTrOROwr1RmI=\");\n_c = ProductPage;\nvar _c;\n$RefreshReg$(_c, \"ProductPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/product/[id]/page.tsx\n"));

/***/ })

});