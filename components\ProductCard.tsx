import Image from 'next/image'
import Link from 'next/link'

interface Product {
  id: string
  name: string
  brand: string
  price: number
  originalPrice?: number
  image: string
  isOnSale: boolean
}

interface ProductCardProps {
  product: Product
}

export default function ProductCard({ product }: ProductCardProps) {
  return (
    <Link href={`/product/${product.id}`} className="group block">
      <div className="product-card">
        {/* Image Container */}
        <div className="product-image-container">
          <Image
            src={product.image}
            alt={product.name}
            fill
            className="object-cover group-hover:scale-105 transition-transform duration-300"
            sizes="(max-width: 640px) 50vw, (max-width: 1024px) 33vw, 25vw"
          />

          {product.isOnSale && (
            <div className="sale-badge">
              Sale
            </div>
          )}
        </div>

        {/* Product Info */}
        <div className="product-info">
          <p className="brand-name">{product.brand}</p>
          <h3 className="product-name">{product.name}</h3>

          <div className="price-container">
            <span className="current-price">£{product.price}</span>
            {product.originalPrice && (
              <span className="original-price">£{product.originalPrice}</span>
            )}
          </div>
        </div>
      </div>
    </Link>
  )
}