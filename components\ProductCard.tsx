import Image from 'next/image'
import Link from 'next/link'

interface Product {
  id: string
  name: string
  brand: string
  price: number
  originalPrice?: number
  image: string
  isOnSale: boolean
}

interface ProductCardProps {
  product: Product
}

export default function ProductCard({ product }: ProductCardProps) {
  return (
    <Link href={`/product/${product.id}`} className="group block">
      <div className="product-card group-hover:shadow-lg transition-all duration-300">
        {/* Image Container */}
        <div className="product-image-container relative overflow-hidden">
          <Image
            src={product.image}
            alt={product.name}
            fill
            className="object-cover group-hover:scale-105 transition-transform duration-500"
            sizes="(max-width: 640px) 50vw, (max-width: 1024px) 33vw, 25vw"
          />

          {product.isOnSale && (
            <div className="sale-badge">
              Sale
            </div>
          )}

          {/* Quick Add Button - appears on hover */}
          <div className="absolute inset-x-4 bottom-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <button className="w-full bg-black text-white py-2 px-4 text-sm font-medium hover:bg-gray-800 transition-colors">
              Quick Add
            </button>
          </div>
        </div>

        {/* Product Info */}
        <div className="product-info">
          <p className="brand-name text-gray-500 text-sm mb-1">{product.brand}</p>
          <h3 className="product-name text-black font-normal mb-2 line-clamp-2">{product.name}</h3>

          <div className="price-container flex items-center gap-2">
            <span className="current-price text-black font-semibold">£{product.price}</span>
            {product.originalPrice && (
              <span className="original-price text-gray-400 text-sm line-through">£{product.originalPrice}</span>
            )}
          </div>
        </div>
      </div>
    </Link>
  )
}