
'use client'

import Link from 'next/link'
import { useState, useRef, useEffect } from 'react'
import { 
  MagnifyingGlassIcon, 
  HeartIcon, 
  ShoppingBagIcon,
  UserIcon,
  Bars3Icon,
  XMarkIcon,
  ChevronDownIcon
} from '@heroicons/react/24/outline'

const megaMenuItems = {
  women: {
    categories: [
      { name: 'New In', items: ['Just In', 'This Week', 'Last 7 Days'] },
      { name: 'Clothing', items: ['Dresses', 'Tops', 'Knitwear', 'Jackets', 'Trousers', 'Skirts'] },
      { name: 'Shoes', items: ['Heels', 'Flats', 'Sneakers', 'Boots', 'Sandals'] },
      { name: 'Bags', items: ['Handbags', 'Shoulder Bags', 'Clutches', 'Backpacks'] },
      { name: 'Accessories', items: ['Jewelry', 'Scarves', 'Sunglasses', 'Belts'] }
    ]
  },
  men: {
    categories: [
      { name: 'New In', items: ['Just In', 'This Week', 'Last 7 Days'] },
      { name: 'Clothing', items: ['T-Shirts', 'Shirts', 'Knitwear', 'Jackets', 'Trousers', 'Jeans'] },
      { name: 'Shoes', items: ['Sneakers', 'Dress Shoes', 'Boots', 'Loafers'] },
      { name: 'Bags', items: ['Briefcases', 'Backpacks', 'Messenger Bags'] },
      { name: 'Accessories', items: ['Watches', 'Wallets', 'Sunglasses', 'Belts'] }
    ]
  }
}

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isSearchOpen, setIsSearchOpen] = useState(false)
  const [activeMegaMenu, setActiveMegaMenu] = useState<string | null>(null)
  const [expandedMobileMenu, setExpandedMobileMenu] = useState<string | null>(null)
  const [isMobile, setIsMobile] = useState(false)
  const megaMenuRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (megaMenuRef.current && !megaMenuRef.current.contains(event.target as Node)) {
        setActiveMegaMenu(null)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleMegaMenuEnter = (menu: string) => {
    if (!isMobile) {
      setActiveMegaMenu(menu)
    }
  }

  const handleMegaMenuLeave = () => {
    if (!isMobile) {
      setActiveMegaMenu(null)
    }
  }

  const toggleMobileSubmenu = (menu: string) => {
    setExpandedMobileMenu(expandedMobileMenu === menu ? null : menu)
  }

  return (
    <>
      {/* Top Banner */}
      <div className="bg-black text-white text-center py-2 text-xs font-medium">
        <p>FREE WORLDWIDE DELIVERY ON ORDERS OVER £200</p>
      </div>

      {/* Main Header */}
      <header className="header" ref={megaMenuRef}>
        <div className="container">
          <div className="header-content">
            {/* Mobile menu button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="mobile-menu-button"
              aria-label="Toggle menu"
            >
              {isMenuOpen ? <XMarkIcon className="h-5 w-5" /> : <Bars3Icon className="h-5 w-5" />}
            </button>

            {/* Logo */}
            <Link href="/" className="flex-shrink-0">
              <h1 className="text-xl font-bold tracking-wide text-black">MATCHES</h1>
            </Link>

            {/* Desktop Navigation */}
            <nav className="header-nav">
              <div
                className="relative"
                onMouseEnter={() => handleMegaMenuEnter('women')}
                onMouseLeave={handleMegaMenuLeave}
              >
                <Link href="/women" className="header-nav-item">
                  Women
                  <ChevronDownIcon className="ml-1 h-3 w-3" />
                </Link>
              </div>

              <div
                className="relative"
                onMouseEnter={() => handleMegaMenuEnter('men')}
                onMouseLeave={handleMegaMenuLeave}
              >
                <Link href="/men" className="header-nav-item">
                  Men
                  <ChevronDownIcon className="ml-1 h-3 w-3" />
                </Link>
              </div>

              <Link href="/just-in" className="header-nav-item">
                Just In
              </Link>
              <Link href="/designers" className="header-nav-item">
                Designers
              </Link>
              <Link href="/shoes" className="header-nav-item">
                Shoes
              </Link>
              <Link href="/bags" className="header-nav-item">
                Bags
              </Link>
              <Link href="/accessories" className="header-nav-item">
                Accessories
              </Link>
              <Link href="/sale" className="header-nav-item sale">
                Sale
              </Link>
            </nav>

            {/* Right side icons */}
            <div className="header-icons">
              <button
                onClick={() => setIsSearchOpen(!isSearchOpen)}
                className="header-icon"
                aria-label="Search"
              >
                <MagnifyingGlassIcon className="h-5 w-5" />
              </button>

              <Link
                href="/account"
                className="header-icon hidden sm:flex"
                aria-label="Account"
              >
                <UserIcon className="h-5 w-5" />
              </Link>

              <Link
                href="/wishlist"
                className="header-icon"
                aria-label="Wishlist"
              >
                <HeartIcon className="h-5 w-5" />
                <span className="header-icon-badge">0</span>
              </Link>

              <Link
                href="/bag"
                className="header-icon"
                aria-label="Shopping bag"
              >
                <ShoppingBagIcon className="h-5 w-5" />
                <span className="header-icon-badge">0</span>
              </Link>
            </div>
          </div>

          {/* Search Bar */}
          {isSearchOpen && (
            <div className="border-t border-gray-200 py-4">
              <div className="flex items-center gap-4">
                <div className="flex-1 relative">
                  <input
                    type="text"
                    placeholder="Search for products, designers..."
                    className="w-full px-4 py-3 border border-gray-300 bg-white text-black focus:outline-none focus:border-black text-sm"
                    autoFocus
                  />
                  <MagnifyingGlassIcon className="absolute right-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                </div>
                <button
                  onClick={() => setIsSearchOpen(false)}
                  className="text-sm font-medium hover:text-gray-600 text-black px-4"
                >
                  Cancel
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Mega Menu */}
        {activeMegaMenu && megaMenuItems[activeMegaMenu as keyof typeof megaMenuItems] && (
          <div
            className="mega-menu"
            onMouseEnter={() => setActiveMegaMenu(activeMegaMenu)}
            onMouseLeave={handleMegaMenuLeave}
          >
            <div className="mega-menu-content">
              <div className="mega-menu-grid">
                {megaMenuItems[activeMegaMenu as keyof typeof megaMenuItems].categories.map((category) => (
                  <div key={category.name} className="mega-menu-category">
                    <h3 className="mega-menu-category-title">
                      {category.name}
                    </h3>
                    <div className="mega-menu-items">
                      {category.items.map((item) => (
                        <Link
                          key={item}
                          href={`/${activeMegaMenu}/${item.toLowerCase().replace(/\s+/g, '-')}`}
                          className="mega-menu-item"
                        >
                          {item}
                        </Link>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="mobile-menu">
            <div className="mobile-menu-content">
              {/* Women Menu */}
              <div className="mobile-menu-section">
                <button
                  onClick={() => toggleMobileSubmenu('women')}
                  className="mobile-menu-toggle"
                >
                  Women
                  <ChevronDownIcon className={`mobile-menu-toggle-icon ${expandedMobileMenu === 'women' ? 'expanded' : ''}`} />
                </button>
                {expandedMobileMenu === 'women' && (
                  <div className="mobile-menu-submenu">
                    <Link href="/women/new-in" className="mobile-menu-item">New In</Link>
                    <Link href="/women/clothing" className="mobile-menu-item">Clothing</Link>
                    <Link href="/women/shoes" className="mobile-menu-item">Shoes</Link>
                    <Link href="/women/bags" className="mobile-menu-item">Bags</Link>
                    <Link href="/women/accessories" className="mobile-menu-item">Accessories</Link>
                  </div>
                )}
              </div>

              {/* Men Menu */}
              <div className="mobile-menu-section">
                <button
                  onClick={() => toggleMobileSubmenu('men')}
                  className="mobile-menu-toggle"
                >
                  Men
                  <ChevronDownIcon className={`mobile-menu-toggle-icon ${expandedMobileMenu === 'men' ? 'expanded' : ''}`} />
                </button>
                {expandedMobileMenu === 'men' && (
                  <div className="mobile-menu-submenu">
                    <Link href="/men/new-in" className="mobile-menu-item">New In</Link>
                    <Link href="/men/clothing" className="mobile-menu-item">Clothing</Link>
                    <Link href="/men/shoes" className="mobile-menu-item">Shoes</Link>
                    <Link href="/men/bags" className="mobile-menu-item">Bags</Link>
                    <Link href="/men/accessories" className="mobile-menu-item">Accessories</Link>
                  </div>
                )}
              </div>

              <Link href="/just-in" className="mobile-menu-item">Just In</Link>
              <Link href="/designers" className="mobile-menu-item">Designers</Link>
              <Link href="/sale" className="mobile-menu-item" style={{color: '#dc2626'}}>Sale</Link>

              <div className="mobile-menu-divider"></div>

              <Link href="/account" className="mobile-menu-item">
                <UserIcon className="h-4 w-4 mr-2" />
                My Account
              </Link>
              <Link href="/wishlist" className="mobile-menu-item">
                <HeartIcon className="h-4 w-4 mr-2" />
                Wishlist
              </Link>
            </div>
          </div>
        )}
      </header>
    </>
  )
}
