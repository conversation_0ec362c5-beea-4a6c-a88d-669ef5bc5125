
'use client'

import Link from 'next/link'
import { useState, useRef, useEffect } from 'react'
import { 
  MagnifyingGlassIcon, 
  HeartIcon, 
  ShoppingBagIcon,
  UserIcon,
  Bars3Icon,
  XMarkIcon,
  ChevronDownIcon
} from '@heroicons/react/24/outline'

const megaMenuItems = {
  women: {
    categories: [
      { name: 'New In', items: ['Just In', 'This Week', 'Last 7 Days'] },
      { name: 'Clothing', items: ['Dresses', 'Tops', 'Knitwear', 'Jackets', 'Trousers', 'Skirts'] },
      { name: 'Shoes', items: ['Heels', 'Flats', 'Sneakers', 'Boots', 'Sandals'] },
      { name: 'Bags', items: ['Handbags', 'Shoulder Bags', 'Clutches', 'Backpacks'] },
      { name: 'Accessories', items: ['Jewelry', 'Scarves', 'Sunglasses', 'Belts'] }
    ]
  },
  men: {
    categories: [
      { name: 'New In', items: ['Just In', 'This Week', 'Last 7 Days'] },
      { name: 'Clothing', items: ['T-Shirts', 'Shirts', 'Knitwear', 'Jackets', 'Trousers', 'Jeans'] },
      { name: 'Shoes', items: ['Sneakers', 'Dress Shoes', 'Boots', 'Loafers'] },
      { name: 'Bags', items: ['Briefcases', 'Backpacks', 'Messenger Bags'] },
      { name: 'Accessories', items: ['Watches', 'Wallets', 'Sunglasses', 'Belts'] }
    ]
  }
}

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isSearchOpen, setIsSearchOpen] = useState(false)
  const [activeMegaMenu, setActiveMegaMenu] = useState<string | null>(null)
  const [expandedMobileMenu, setExpandedMobileMenu] = useState<string | null>(null)
  const [isMobile, setIsMobile] = useState(false)
  const megaMenuRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (megaMenuRef.current && !megaMenuRef.current.contains(event.target as Node)) {
        setActiveMegaMenu(null)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleMegaMenuEnter = (menu: string) => {
    if (!isMobile) {
      setActiveMegaMenu(menu)
    }
  }

  const handleMegaMenuLeave = () => {
    if (!isMobile) {
      setActiveMegaMenu(null)
    }
  }

  const toggleMobileSubmenu = (menu: string) => {
    setExpandedMobileMenu(expandedMobileMenu === menu ? null : menu)
  }

  return (
    <>
      {/* Top Banner */}
      <div className="bg-black text-white text-center py-2 text-xs font-medium">
        <p>FREE WORLDWIDE DELIVERY ON ORDERS OVER £200</p>
      </div>

      {/* Main Header */}
      <header className="header" ref={megaMenuRef}>
        <div className="container">
          <div className="header-content">
            {/* Mobile menu button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="lg:hidden p-2 text-black hover:bg-gray-100 rounded-full transition-colors"
              aria-label="Toggle menu"
            >
              {isMenuOpen ? <XMarkIcon className="h-6 w-6" /> : <Bars3Icon className="h-6 w-6" />}
            </button>

            {/* Logo */}
            <Link href="/" className="flex-shrink-0">
              <h1 className="text-xl font-bold tracking-wide text-black">MATCHES</h1>
            </Link>

            {/* Desktop Navigation */}
            <nav className="hidden lg:flex items-center space-x-6 text-sm font-medium text-black">
              <div
                className="relative"
                onMouseEnter={() => handleMegaMenuEnter('women')}
                onMouseLeave={handleMegaMenuLeave}
              >
                <Link href="/women" className="flex items-center hover:text-gray-600 transition-colors py-4">
                  Women
                  <ChevronDownIcon className="ml-1 h-3 w-3" />
                </Link>
              </div>

              <div
                className="relative"
                onMouseEnter={() => handleMegaMenuEnter('men')}
                onMouseLeave={handleMegaMenuLeave}
              >
                <Link href="/men" className="flex items-center hover:text-gray-600 transition-colors py-4">
                  Men
                  <ChevronDownIcon className="ml-1 h-3 w-3" />
                </Link>
              </div>

              <Link href="/just-in" className="hover:text-gray-600 transition-colors py-4">
                Just In
              </Link>
              <Link href="/designers" className="hover:text-gray-600 transition-colors py-4">
                Designers
              </Link>
              <Link href="/shoes" className="hover:text-gray-600 transition-colors py-4">
                Shoes
              </Link>
              <Link href="/bags" className="hover:text-gray-600 transition-colors py-4">
                Bags
              </Link>
              <Link href="/accessories" className="hover:text-gray-600 transition-colors py-4">
                Accessories
              </Link>
              <Link href="/sale" className="hover:text-gray-600 transition-colors py-4 text-red-600 font-semibold">
                Sale
              </Link>
            </nav>

            {/* Right side icons */}
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setIsSearchOpen(!isSearchOpen)}
                className="p-2 hover:bg-gray-100 rounded-full transition-colors text-black"
                aria-label="Search"
              >
                <MagnifyingGlassIcon className="h-5 w-5" />
              </button>

              <Link
                href="/account"
                className="hidden sm:block p-2 hover:bg-gray-100 rounded-full transition-colors text-black"
                aria-label="Account"
              >
                <UserIcon className="h-5 w-5" />
              </Link>

              <Link
                href="/wishlist"
                className="p-2 hover:bg-gray-100 rounded-full transition-colors relative text-black"
                aria-label="Wishlist"
              >
                <HeartIcon className="h-5 w-5" />
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">0</span>
              </Link>

              <Link
                href="/bag"
                className="p-2 hover:bg-gray-100 rounded-full transition-colors relative text-black"
                aria-label="Shopping bag"
              >
                <ShoppingBagIcon className="h-5 w-5" />
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">0</span>
              </Link>
            </div>
          </div>

          {/* Search Bar */}
          {isSearchOpen && (
            <div className="border-t border-gray-200 py-4">
              <div className="flex items-center gap-4">
                <div className="flex-1 relative">
                  <input
                    type="text"
                    placeholder="Search for products, designers..."
                    className="w-full px-4 py-3 border border-gray-300 bg-white text-black rounded-none focus:outline-none focus:border-black text-sm"
                    autoFocus
                  />
                  <MagnifyingGlassIcon className="absolute right-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-neutral-400" />
                </div>
                <button
                  onClick={() => setIsSearchOpen(false)}
                  className="text-sm font-medium hover:text-gray-600 text-black px-4"
                >
                  Cancel
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Mega Menu */}
        {activeMegaMenu && megaMenuItems[activeMegaMenu as keyof typeof megaMenuItems] && (
          <div 
            className="absolute top-full left-0 w-full bg-white shadow-lg border-t border-neutral-200 z-40"
            onMouseEnter={() => setActiveMegaMenu(activeMegaMenu)}
            onMouseLeave={handleMegaMenuLeave}
          >
            <div className="max-w-7xl mx-auto px-4 py-8">
              <div className="grid grid-cols-5 gap-8">
                {megaMenuItems[activeMegaMenu as keyof typeof megaMenuItems].categories.map((category) => (
                  <div key={category.name}>
                    <h3 className="font-semibold text-neutral-900 mb-4 text-sm uppercase tracking-wide">
                      {category.name}
                    </h3>
                    <ul className="space-y-2">
                      {category.items.map((item) => (
                        <li key={item}>
                          <Link 
                            href={`/${activeMegaMenu}/${item.toLowerCase().replace(/\s+/g, '-')}`}
                            className="text-sm text-neutral-600 hover:text-neutral-900 transition-colors block py-1"
                          >
                            {item}
                          </Link>
                        </li>
                      ))}
                    </ul>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="lg:hidden border-t border-gray-200 bg-white">
            <nav className="px-4 py-4 space-y-1 text-black max-h-96 overflow-y-auto">
              {/* Women Menu */}
              <div className="py-2">
                <button 
                  onClick={() => toggleMobileSubmenu('women')}
                  className="flex items-center justify-between w-full text-left text-sm font-medium py-2"
                >
                  Women
                  <ChevronDownIcon className={`h-4 w-4 transition-transform ${expandedMobileMenu === 'women' ? 'rotate-180' : ''}`} />
                </button>
                {expandedMobileMenu === 'women' && (
                  <div className="pl-4 space-y-1 mt-2">
                    <Link href="/women/new-in" className="block text-sm py-1 text-gray-600">New In</Link>
                    <Link href="/women/clothing" className="block text-sm py-1 text-gray-600">Clothing</Link>
                    <Link href="/women/shoes" className="block text-sm py-1 text-gray-600">Shoes</Link>
                    <Link href="/women/bags" className="block text-sm py-1 text-gray-600">Bags</Link>
                    <Link href="/women/accessories" className="block text-sm py-1 text-gray-600">Accessories</Link>
                  </div>
                )}
              </div>

              {/* Men Menu */}
              <div className="py-2">
                <button
                  onClick={() => toggleMobileSubmenu('men')}
                  className="flex items-center justify-between w-full text-left text-sm font-medium py-2"
                >
                  Men
                  <ChevronDownIcon className={`h-4 w-4 transition-transform ${expandedMobileMenu === 'men' ? 'rotate-180' : ''}`} />
                </button>
                {expandedMobileMenu === 'men' && (
                  <div className="pl-4 space-y-1 mt-2">
                    <Link href="/men/new-in" className="block text-sm py-1 text-gray-600">New In</Link>
                    <Link href="/men/clothing" className="block text-sm py-1 text-gray-600">Clothing</Link>
                    <Link href="/men/shoes" className="block text-sm py-1 text-gray-600">Shoes</Link>
                    <Link href="/men/bags" className="block text-sm py-1 text-gray-600">Bags</Link>
                    <Link href="/men/accessories" className="block text-sm py-1 text-gray-600">Accessories</Link>
                  </div>
                )}
              </div>

              <Link href="/just-in" className="block text-sm font-medium py-2">Just In</Link>
              <Link href="/designers" className="block text-sm font-medium py-2">Designers</Link>
              <Link href="/sale" className="block text-sm font-medium py-2 text-red-600">Sale</Link>

              <hr className="border-gray-200 my-4" />

              <Link href="/account" className="flex items-center text-sm font-medium py-2">
                <UserIcon className="h-4 w-4 mr-2" />
                My Account
              </Link>
              <Link href="/wishlist" className="flex items-center text-sm font-medium py-2">
                <HeartIcon className="h-4 w-4 mr-2" />
                Wishlist
              </Link>
            </nav>
          </div>
        )}
      </header>
    </>
  )
}
