import Link from 'next/link'
import Image from 'next/image'

const newArrivals = [
  {
    id: 1,
    name: 'Oversized blazer',
    brand: 'Toteme',
    price: '£650',
    image: '/products/toteme-blazer.jpg',
    isNew: true
  },
  {
    id: 2,
    name: 'Silk slip dress',
    brand: 'The Row',
    price: '£1,890',
    image: '/products/the-row-dress.jpg',
    isNew: true
  },
  {
    id: 3,
    name: 'Leather boots',
    brand: 'Bottega Veneta',
    price: '£1,200',
    image: '/products/bottega-boots.jpg',
    isNew: true
  },
  {
    id: 4,
    name: 'Cashmere scarf',
    brand: 'Acne Studios',
    price: '£280',
    image: '/products/acne-scarf.jpg',
    isNew: true
  },
  {
    id: 5,
    name: 'Wool coat',
    brand: 'Ganni',
    price: '£495',
    image: '/products/ganni-coat.jpg',
    isNew: true
  },
  {
    id: 6,
    name: 'Mini bag',
    brand: '<PERSON><PERSON><PERSON><PERSON>',
    price: '£520',
    image: '/products/jacquemus-bag.jpg',
    isNew: true
  }
]

export default function JustInPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Breadcrumb */}
      <div className="border-b border-gray-200">
        <div className="container mx-auto px-4 py-4">
          <nav className="text-sm text-gray-500">
            <Link href="/" className="hover:text-black">Home</Link>
            <span className="mx-2">/</span>
            <span className="text-black">Just In</span>
          </nav>
        </div>
      </div>

      {/* Hero Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl lg:text-5xl font-light text-black mb-6">JUST IN</h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto mb-8">
            Discover the latest arrivals from your favorite designers. Fresh styles, new collections, and must-have pieces that just landed.
          </p>
          <div className="flex justify-center gap-4">
            <Link href="/women" className="bg-black text-white px-8 py-3 text-sm font-medium hover:bg-gray-800 transition-colors">
              SHOP WOMEN
            </Link>
            <Link href="/men" className="border border-black text-black px-8 py-3 text-sm font-medium hover:bg-black hover:text-white transition-colors">
              SHOP MEN
            </Link>
          </div>
        </div>
      </section>

      {/* New Arrivals Grid */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-2xl font-light text-black">New This Week</h2>
            <div className="text-sm text-gray-600">{newArrivals.length} items</div>
          </div>

          <div className="grid grid-cols-2 lg:grid-cols-3 gap-6">
            {newArrivals.map((product) => (
              <Link key={product.id} href={`/product/${product.id}`} className="group">
                <div className="relative aspect-[3/4] mb-4 overflow-hidden bg-gray-100">
                  {product.isNew && (
                    <div className="absolute top-4 left-4 bg-black text-white px-2 py-1 text-xs font-medium z-10">
                      NEW
                    </div>
                  )}
                  <Image
                    src={product.image}
                    alt={product.name}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <div className="space-y-1">
                  <p className="text-xs text-gray-500 uppercase tracking-wide">{product.brand}</p>
                  <h3 className="text-sm font-medium text-black line-clamp-2">{product.name}</h3>
                  <p className="text-sm font-medium text-black">{product.price}</p>
                </div>
              </Link>
            ))}
          </div>

          {/* Load More */}
          <div className="text-center mt-12">
            <button className="border border-black text-black px-8 py-3 text-sm font-medium hover:bg-black hover:text-white transition-colors">
              LOAD MORE
            </button>
          </div>
        </div>
      </section>

      {/* Featured Categories */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl font-light text-black text-center mb-12">Shop New Arrivals by Category</h2>
          
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
            {[
              { name: 'NEW DRESSES', image: '/category-new-dresses.jpg', href: '/women/dresses' },
              { name: 'NEW SHOES', image: '/category-new-shoes.jpg', href: '/women/shoes' },
              { name: 'NEW BAGS', image: '/category-new-bags.jpg', href: '/women/bags' },
              { name: 'NEW ACCESSORIES', image: '/category-new-accessories.jpg', href: '/accessories' }
            ].map((category) => (
              <Link key={category.name} href={category.href} className="group">
                <div className="relative aspect-square mb-4 overflow-hidden bg-gray-100">
                  <Image
                    src={category.image}
                    alt={category.name}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <h3 className="text-sm font-medium text-center">{category.name}</h3>
              </Link>
            ))}
          </div>
        </div>
      </section>
    </div>
  )
}
