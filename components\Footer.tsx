
export default function Footer() {
  return (
    <footer className="bg-gray-50 text-gray-600 text-sm py-16 mt-auto">
      <div className="max-w-7xl mx-auto px-4">
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8 mb-12">
          {/* MATCHES */}
          <div className="col-span-2 md:col-span-1">
            <h4 className="font-semibold mb-4 text-black text-base">MATCHES</h4>
            <ul className="space-y-3">
              <li><a href="#" className="hover:text-black transition-colors">About Us</a></li>
              <li><a href="#" className="hover:text-black transition-colors">Careers</a></li>
              <li><a href="#" className="hover:text-black transition-colors">Affiliates</a></li>
              <li><a href="#" className="hover:text-black transition-colors">Press</a></li>
            </ul>
          </div>

          {/* Customer Care */}
          <div>
            <h4 className="font-semibold mb-4 text-black text-base">Customer Care</h4>
            <ul className="space-y-3">
              <li><a href="#" className="hover:text-black transition-colors">Contact Us</a></li>
              <li><a href="#" className="hover:text-black transition-colors">Size Guide</a></li>
              <li><a href="#" className="hover:text-black transition-colors">Delivery</a></li>
              <li><a href="#" className="hover:text-black transition-colors">Returns</a></li>
            </ul>
          </div>

          {/* Services */}
          <div>
            <h4 className="font-semibold mb-4 text-black text-base">Services</h4>
            <ul className="space-y-3">
              <li><a href="#" className="hover:text-black transition-colors">Personal Shopping</a></li>
              <li><a href="#" className="hover:text-black transition-colors">Gift Cards</a></li>
              <li><a href="#" className="hover:text-black transition-colors">Loyalty Program</a></li>
              <li><a href="#" className="hover:text-black transition-colors">Style Advice</a></li>
            </ul>
          </div>

          {/* Legal */}
          <div>
            <h4 className="font-semibold mb-4 text-black text-base">Legal</h4>
            <ul className="space-y-3">
              <li><a href="#" className="hover:text-black transition-colors">Terms & Conditions</a></li>
              <li><a href="#" className="hover:text-black transition-colors">Privacy Policy</a></li>
              <li><a href="#" className="hover:text-black transition-colors">Cookie Policy</a></li>
            </ul>
          </div>

          {/* Connect */}
          <div>
            <h4 className="font-semibold mb-4 text-black text-base">Connect</h4>
            <ul className="space-y-3">
              <li><a href="#" className="hover:text-black transition-colors">Instagram</a></li>
              <li><a href="#" className="hover:text-black transition-colors">Twitter</a></li>
              <li><a href="#" className="hover:text-black transition-colors">Facebook</a></li>
              <li><a href="#" className="hover:text-black transition-colors">Pinterest</a></li>
            </ul>
          </div>
        </div>

        {/* Newsletter Signup */}
        <div className="border-t border-gray-200 pt-8 mb-8">
          <div className="max-w-md mx-auto text-center">
            <h4 className="font-semibold mb-4 text-black text-base">Stay in the know</h4>
            <p className="text-gray-600 mb-4">Get the latest news and exclusive offers</p>
            <div className="flex gap-2">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-4 py-2 border border-gray-300 focus:outline-none focus:border-black text-sm"
              />
              <button className="bg-black text-white px-6 py-2 text-sm font-medium hover:bg-gray-800 transition-colors">
                Subscribe
              </button>
            </div>
          </div>
        </div>

        {/* Bottom section */}
        <div className="border-t border-gray-200 pt-8">
          <div className="flex flex-col lg:flex-row justify-between items-center gap-4 text-center lg:text-left">
            <div className="text-xs text-gray-500">
              © 2024 MATCHES. All rights reserved.
            </div>

            <div className="text-xs text-gray-500">
              Shipping worldwide | Customer service: +44 (0)20 7647 8888
            </div>

            <div className="flex items-center gap-4">
              <span className="text-xs text-gray-500">Download our app:</span>
              <div className="flex gap-2">
                <a href="#" className="bg-black text-white px-3 py-1 text-xs hover:bg-gray-800 transition-colors">App Store</a>
                <a href="#" className="bg-black text-white px-3 py-1 text-xs hover:bg-gray-800 transition-colors">Google Play</a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
