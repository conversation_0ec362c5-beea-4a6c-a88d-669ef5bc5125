'use client'

import Image from 'next/image'
import Link from 'next/link'

const categories = [
  {
    id: 'women',
    name: 'Women',
    image: '/carousel/women.svg',
    href: '/category/women'
  },
  {
    id: 'men',
    name: 'Men',
    image: '/carousel/men.svg',
    href: '/category/men'
  },
  {
    id: 'bags',
    name: 'Bags',
    image: '/carousel/bags.svg',
    href: '/category/bags'
  },
  {
    id: 'shoes',
    name: 'Shoes',
    image: '/carousel/shoes.svg',
    href: '/category/shoes'
  }
]

export default function CategoryCarousel() {
  return (
    <div className="category-carousel">
      <div className="category-grid">
        {categories.map((category) => (
          <Link
            key={category.id}
            href={category.href}
            className="category-item"
          >
            <div className="category-image">
              <Image
                src={category.image}
                alt={category.name}
                fill
                sizes="(max-width: 768px) 160px, 200px"
              />
            </div>
            <h3 className="category-name">{category.name}</h3>
          </Link>
        ))}
      </div>
    </div>
  )
}