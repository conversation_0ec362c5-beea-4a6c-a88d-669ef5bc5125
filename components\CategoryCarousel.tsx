'use client'

import Image from 'next/image'
import Link from 'next/link'

const categories = [
  {
    id: 'women',
    name: 'Women',
    image: '/carousel/women.svg',
    href: '/category/women'
  },
  {
    id: 'men',
    name: 'Men',
    image: '/carousel/men.svg',
    href: '/category/men'
  },
  {
    id: 'bags',
    name: 'Bags',
    image: '/carousel/bags.svg',
    href: '/category/bags'
  },
  {
    id: 'shoes',
    name: 'Shoes',
    image: '/carousel/shoes.svg',
    href: '/category/shoes'
  }
]

export default function CategoryCarousel() {
  return (
    <div className="w-full py-8">
      <div className="flex gap-6 overflow-x-auto scrollbar-hide px-4 md:px-6 lg:px-8">
        {categories.map((category) => (
          <Link
            key={category.id}
            href={category.href}
            className="flex-none group"
          >
            <div className="w-40 h-40 md:w-48 md:h-48 relative overflow-hidden rounded-lg bg-gray-100 transition-all duration-300 group-hover:scale-105 group-hover:shadow-lg">
              <Image
                src={category.image}
                alt={category.name}
                fill
                className="object-cover transition-transform duration-300 group-hover:scale-110"
                sizes="(max-width: 768px) 160px, 192px"
              />
              {/* Overlay */}
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300" />
              {/* Category name overlay */}
              <div className="absolute inset-0 flex items-center justify-center">
                <h3 className="text-white text-lg font-semibold text-center opacity-0 group-hover:opacity-100 transition-opacity duration-300 transform translate-y-2 group-hover:translate-y-0">
                  {category.name}
                </h3>
              </div>
            </div>
            <h3 className="mt-3 text-sm font-medium text-center text-gray-900 group-hover:text-black transition-colors">
              {category.name}
            </h3>
          </Link>
        ))}
      </div>
    </div>
  )
}