'use client'

import Image from 'next/image'
import Link from 'next/link'

const categories = [
  {
    id: 'women',
    name: 'Women',
    image: '/carousel/women.svg',
    href: '/category/women'
  },
  {
    id: 'men',
    name: 'Men',
    image: '/carousel/men.svg',
    href: '/category/men'
  },
  {
    id: 'bags',
    name: 'Bags',
    image: '/carousel/bags.svg',
    href: '/category/bags'
  },
  {
    id: 'shoes',
    name: 'Shoes',
    image: '/carousel/shoes.svg',
    href: '/category/shoes'
  }
]

export default function CategoryCarousel() {
  return (
    <div className="w-full py-8">
      <div className="flex gap-6 overflow-x-auto scrollbar-hide px-4 md:px-6 lg:px-8">
        {categories.map((category) => (
          <Link
            key={category.id}
            href={category.href}
            className="flex-none group"
          >
            <div className="w-32 h-32 md:w-40 md:h-40 relative overflow-hidden rounded-lg bg-gray-100 transition-transform group-hover:scale-105">
              <Image
                src={category.image}
                alt={category.name}
                fill
                className="object-cover"
                sizes="(max-width: 768px) 128px, 160px"
              />
            </div>
            <h3 className="mt-3 text-sm font-medium text-center text-gray-900">
              {category.name}
            </h3>
          </Link>
        ))}
      </div>
    </div>
  )
}