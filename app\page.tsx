import SaleBanner from '@/components/SaleBanner'
import CategoryCarousel from '@/components/CategoryCarousel'
import ProductCard from '@/components/ProductCard'

// Mock product data
const featuredProducts = [
  {
    id: '1',
    name: 'Classic Cotton Shirt',
    brand: 'Designer Brand',
    price: 299,
    originalPrice: 399,
    image: '/products/product-1.svg',
    isOnSale: true
  },
  {
    id: '2',
    name: 'Luxury Wool Coat',
    brand: 'Premium Label',
    price: 899,
    image: '/products/product-2.svg',
    isOnSale: false
  },
  {
    id: '3',
    name: 'Designer Handbag',
    brand: 'Fashion House',
    price: 1299,
    originalPrice: 1599,
    image: '/products/product-3.svg',
    isOnSale: true
  },
  {
    id: '4',
    name: 'Premium Sneakers',
    brand: 'Sport Luxury',
    price: 499,
    image: '/products/product-4.svg',
    isOnSale: false
  }
]

export default function Home() {
  return (
    <>
      <SaleBanner />

      {/* Hero Section */}
      <section className="hero-section">
        <div className="container">
          <div className="hero-content">
            <h1 className="hero-title">
              Luxury Fashion
            </h1>
            <p className="hero-description">
              Discover our curated collection of designer clothing, bags, and accessories from the world's most prestigious brands.
            </p>
            <div className="hero-actions">
              <button className="btn btn-primary">
                Shop Women
              </button>
              <button className="btn btn-secondary">
                Shop Men
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Category Carousel */}
      <section className="section">
        <div className="container">
          <h2 className="section-title">Shop by Category</h2>
          <CategoryCarousel />
        </div>
      </section>

      {/* Featured Products */}
      <section className="section section-alt">
        <div className="container">
          <h2 className="section-title">Featured Products</h2>
          <div className="products-grid">
            {featuredProducts.map((product) => (
              <ProductCard key={product.id} product={product} />
            ))}
          </div>
        </div>
      </section>

      {/* Newsletter Section */}
      <section className="newsletter-section">
        <div className="container">
          <div className="newsletter-content">
            <h2 className="newsletter-title">Stay Updated</h2>
            <p className="newsletter-description">
              Subscribe to our newsletter for exclusive offers and the latest fashion news.
            </p>
            <div className="newsletter-form">
              <input
                type="email"
                placeholder="Enter your email"
                className="newsletter-input"
              />
              <button className="newsletter-button">
                Subscribe
              </button>
            </div>
          </div>
        </div>
      </section>
    </>
  )
}