import SaleBanner from '@/components/SaleBanner'
import CategoryCarousel from '@/components/CategoryCarousel'
import ProductCard from '@/components/ProductCard'

// Mock product data
const featuredProducts = [
  {
    id: '1',
    name: 'Classic Cotton Shirt',
    brand: 'Designer Brand',
    price: 299,
    originalPrice: 399,
    image: '/products/product-1.svg',
    isOnSale: true
  },
  {
    id: '2',
    name: 'Luxury Wool Coat',
    brand: 'Premium Label',
    price: 899,
    image: '/products/product-2.svg',
    isOnSale: false
  },
  {
    id: '3',
    name: 'Designer Handbag',
    brand: 'Fashion House',
    price: 1299,
    originalPrice: 1599,
    image: '/products/product-3.svg',
    isOnSale: true
  },
  {
    id: '4',
    name: 'Premium Sneakers',
    brand: 'Sport Luxury',
    price: 499,
    image: '/products/product-4.svg',
    isOnSale: false
  }
]

export default function Home() {
  return (
    <>
      <SaleBanner />
        {/* Hero Section */}
        <section className="relative bg-gradient-to-br from-gray-50 to-white py-20 md:py-32">
          <div className="max-w-7xl mx-auto container-padding text-center">
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-light text-black mb-6 tracking-tight">
              Luxury Fashion
            </h1>
            <p className="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto mb-10 leading-relaxed">
              Discover our curated collection of designer clothing, bags, and accessories from the world's most prestigious brands.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <button className="btn btn-primary px-8 py-3 text-base font-medium">
                Shop Women
              </button>
              <button className="btn btn-secondary px-8 py-3 text-base font-medium">
                Shop Men
              </button>
            </div>
          </div>
          {/* Decorative elements */}
          <div className="absolute top-10 left-10 w-20 h-20 bg-gray-200 rounded-full opacity-20"></div>
          <div className="absolute bottom-10 right-10 w-32 h-32 bg-gray-300 rounded-full opacity-10"></div>
        </section>

        {/* Category Carousel */}
        <section className="section-padding">
          <div className="max-w-7xl mx-auto">
            <h2 className="text-heading container-padding mb-8">Shop by Category</h2>
            <CategoryCarousel />
          </div>
        </section>

        {/* Featured Products */}
        <section className="section-padding bg-gray-50">
          <div className="max-w-7xl mx-auto container-padding">
            <h2 className="text-heading mb-8">Featured Products</h2>
            <div className="product-grid">
              {featuredProducts.map((product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>
          </div>
        </section>

        {/* Newsletter Section */}
        <section className="section-padding bg-black text-white">
          <div className="max-w-4xl mx-auto container-padding text-center">
            <h2 className="text-3xl md:text-4xl font-light mb-4">Stay Updated</h2>
            <p className="text-gray-300 mb-8 text-lg">
              Subscribe to our newsletter for exclusive offers and the latest fashion news.
            </p>
            <div className="max-w-md mx-auto flex flex-col sm:flex-row gap-4">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-4 py-3 border border-gray-600 bg-transparent text-white placeholder-gray-400 rounded-none focus:outline-none focus:border-white transition-colors"
              />
              <button className="bg-white text-black px-8 py-3 font-medium hover:bg-gray-100 transition-colors">
                Subscribe
              </button>
            </div>
          </div>
        </section>
    </>
  )
}