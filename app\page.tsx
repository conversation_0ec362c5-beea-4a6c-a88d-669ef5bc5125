import Link from 'next/link'
import Image from 'next/image'

export default function Home() {
  return (
    <>
      {/* Hero Banner */}
      <section className="relative bg-green-100 min-h-[600px] flex items-center">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
            <div className="space-y-6">
              <h1 className="text-4xl lg:text-6xl font-light text-black">
                NEW SEASON HEROES
              </h1>
              <p className="text-lg text-gray-700 max-w-md">
                Discover the latest arrivals from your favorite designers
              </p>
              <div className="flex gap-4">
                <Link href="/women" className="bg-black text-white px-8 py-3 text-sm font-medium hover:bg-gray-800 transition-colors">
                  SHOP WOMEN
                </Link>
                <Link href="/men" className="border border-black text-black px-8 py-3 text-sm font-medium hover:bg-black hover:text-white transition-colors">
                  SHOP MEN
                </Link>
              </div>
            </div>
            <div className="relative">
              <Image
                src="/hero-image.jpg"
                alt="New Season Heroes"
                width={600}
                height={600}
                className="w-full h-auto object-cover"
                priority
              />
            </div>
          </div>
        </div>
      </section>

      {/* Featured Categories */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-light text-black mb-4">SHOP BY CATEGORY</h2>
            <div className="w-24 h-px bg-black mx-auto"></div>
          </div>

          <div className="grid grid-cols-2 lg:grid-cols-5 gap-6">
            {[
              { name: 'CLOTHING', count: '545 PRODUCTS', image: '/category-clothing.jpg' },
              { name: 'SHOES', count: '234 PRODUCTS', image: '/category-shoes.jpg' },
              { name: 'BAGS', count: '156 PRODUCTS', image: '/category-bags.jpg' },
              { name: 'ACCESSORIES', count: '89 PRODUCTS', image: '/category-accessories.jpg' },
              { name: 'SALE', count: 'UP TO 70% OFF', image: '/category-sale.jpg' }
            ].map((category) => (
              <Link key={category.name} href={`/${category.name.toLowerCase()}`} className="group">
                <div className="relative aspect-square mb-4 overflow-hidden">
                  <Image
                    src={category.image}
                    alt={category.name}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <h3 className="text-sm font-medium text-black mb-1">{category.name}</h3>
                <p className="text-xs text-gray-500">{category.count}</p>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Editorial Content */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="relative aspect-[4/5]">
              <Image
                src="/editorial-1.jpg"
                alt="Editorial Content"
                fill
                className="object-cover"
              />
              <div className="absolute bottom-8 left-8 text-white">
                <h3 className="text-2xl font-light mb-2">WINTER ESSENTIALS</h3>
                <p className="text-sm mb-4">Discover our curated edit</p>
                <Link href="/winter-essentials" className="text-sm underline">
                  SHOP NOW
                </Link>
              </div>
            </div>
            <div className="space-y-8">
              <div className="relative aspect-[4/3]">
                <Image
                  src="/editorial-2.jpg"
                  alt="Editorial Content"
                  fill
                  className="object-cover"
                />
                <div className="absolute bottom-6 left-6 text-white">
                  <h3 className="text-xl font-light mb-2">THE GOLDEN HOUR</h3>
                  <Link href="/golden-hour" className="text-sm underline">
                    DISCOVER
                  </Link>
                </div>
              </div>
              <div className="grid grid-cols-3 gap-4">
                {[
                  { name: 'DRESSES', image: '/mini-1.jpg' },
                  { name: 'KNITWEAR', image: '/mini-2.jpg' },
                  { name: 'OUTERWEAR', image: '/mini-3.jpg' }
                ].map((item) => (
                  <Link key={item.name} href={`/${item.name.toLowerCase()}`} className="group">
                    <div className="relative aspect-square mb-2 overflow-hidden">
                      <Image
                        src={item.image}
                        alt={item.name}
                        fill
                        className="object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                    </div>
                    <p className="text-xs font-medium text-center">{item.name}</p>
                  </Link>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Newsletter */}
      <section className="py-16 bg-black text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-light mb-4">SIGN UP TO OUR EMAILS</h2>
          <p className="text-gray-300 mb-8 max-w-md mx-auto">
            Be the first to know about new arrivals, exclusive offers and style inspiration
          </p>
          <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
            <input
              type="email"
              placeholder="Enter your email address"
              className="flex-1 px-4 py-3 bg-transparent border border-gray-600 text-white placeholder-gray-400 focus:outline-none focus:border-white"
            />
            <button className="bg-white text-black px-8 py-3 font-medium hover:bg-gray-100 transition-colors">
              SIGN UP
            </button>
          </div>
        </div>
      </section>
    </>
  )
}