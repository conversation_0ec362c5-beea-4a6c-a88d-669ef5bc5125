import Header from '@/components/Header'
import SaleBanner from '@/components/SaleBanner'
import CategoryCarousel from '@/components/CategoryCarousel'
import ProductCard from '@/components/ProductCard'
import Footer from '@/components/Footer'

// Mock product data
const featuredProducts = [
  {
    id: '1',
    name: 'Classic Cotton Shirt',
    brand: 'Designer Brand',
    price: 299,
    originalPrice: 399,
    image: '/products/product-1.svg',
    isOnSale: true
  },
  {
    id: '2',
    name: 'Luxury Wool Coat',
    brand: 'Premium Label',
    price: 899,
    image: '/products/product-2.svg',
    isOnSale: false
  },
  {
    id: '3',
    name: 'Designer Handbag',
    brand: 'Fashion House',
    price: 1299,
    originalPrice: 1599,
    image: '/products/product-3.svg',
    isOnSale: true
  },
  {
    id: '4',
    name: 'Premium Sneakers',
    brand: 'Sport Luxury',
    price: 499,
    image: '/products/product-4.svg',
    isOnSale: false
  }
]

export default function Home() {
  return (
    <div className="min-h-screen flex flex-col bg-white">
      <SaleBanner />
      <Header />

      <main className="flex-1">
        {/* Hero Section */}
        <section className="section-padding bg-gray-50">
          <div className="max-w-7xl mx-auto container-padding text-center">
            <h1 className="text-display mb-6">
              Luxury Fashion
            </h1>
            <p className="text-subheading text-gray-600 max-w-2xl mx-auto mb-8">
              Discover our curated collection of designer clothing, bags, and accessories from the world's most prestigious brands.
            </p>
            <button className="btn btn-primary">
              Shop Now
            </button>
          </div>
        </section>

        {/* Category Carousel */}
        <section className="section-padding">
          <div className="max-w-7xl mx-auto">
            <h2 className="text-heading container-padding mb-8">Shop by Category</h2>
            <CategoryCarousel />
          </div>
        </section>

        {/* Featured Products */}
        <section className="section-padding bg-gray-50">
          <div className="max-w-7xl mx-auto container-padding">
            <h2 className="text-heading mb-8">Featured Products</h2>
            <div className="product-grid">
              {featuredProducts.map((product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>
          </div>
        </section>

        {/* Newsletter Section */}
        <section className="section-padding">
          <div className="max-w-4xl mx-auto container-padding text-center">
            <h2 className="text-heading mb-4">Stay Updated</h2>
            <p className="text-gray-600 mb-8">
              Subscribe to our newsletter for exclusive offers and the latest fashion news.
            </p>
            <div className="max-w-md mx-auto flex gap-4">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-4 py-3 border border-gray-300 rounded-none focus:outline-none focus:border-black"
              />
              <button className="btn btn-primary px-8">
                Subscribe
              </button>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  )
}