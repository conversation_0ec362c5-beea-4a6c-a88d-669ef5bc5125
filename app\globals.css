
@import "tailwindcss";

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 255, 255, 255;
  --background-end-rgb: 255, 255, 255;
  --matches-green: 134, 209, 174;
  --matches-green-light: 166, 223, 195;
  --matches-green-dark: 102, 185, 152;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  line-height: 1.5;
}

body {
  color: rgb(var(--foreground-rgb));
  background: rgb(var(--background-start-rgb));
}

a {
  color: inherit;
  text-decoration: none;
}

/* Custom utilities */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Focus states */
button:focus-visible,
a:focus-visible,
input:focus-visible {
  outline: 2px solid rgb(134, 209, 174);
  outline-offset: 2px;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Enhanced mobile touch targets */
@media (max-width: 768px) {
  button, 
  a[role="button"],
  .touchable {
    min-height: 44px;
    min-width: 44px;
  }
}

/* Loading states */
.loading {
  position: relative;
  overflow: hidden;
}

.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Improved form elements */
input[type="text"],
input[type="email"],
input[type="search"],
textarea {
  appearance: none;
  -webkit-appearance: none;
}

/* Better button styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: colors 0.15s ease;
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(134, 209, 174, 0.5);
}

.btn-primary {
  background-color: black;
  color: white;
}

.btn-primary:hover {
  background-color: #404040;
}

.btn-secondary {
  border: 1px solid black;
  color: black;
  background-color: transparent;
}

.btn-secondary:hover {
  background-color: black;
  color: white;
}

/* Grid improvements */
.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem;
}

@media (max-width: 640px) {
  .product-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }
}

/* Typography scale */
.text-display {
  font-size: 2.25rem;
  font-weight: 300;
}

@media (min-width: 768px) {
  .text-display {
    font-size: 3rem;
  }
}

@media (min-width: 1024px) {
  .text-display {
    font-size: 6rem;
  }
}

.text-heading {
  font-size: 1.5rem;
  font-weight: 300;
}

@media (min-width: 768px) {
  .text-heading {
    font-size: 1.875rem;
  }
}

.text-subheading {
  font-size: 1.125rem;
  font-weight: 500;
}

@media (min-width: 768px) {
  .text-subheading {
    font-size: 1.25rem;
  }
}

/* Spacing utilities */
.section-padding {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

@media (min-width: 768px) {
  .section-padding {
    padding-top: 4rem;
    padding-bottom: 4rem;
  }
}

@media (min-width: 1024px) {
  .section-padding {
    padding-top: 5rem;
    padding-bottom: 5rem;
  }
}

.container-padding {
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 768px) {
  .container-padding {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container-padding {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

/* Product Card Styles */
.product-card {
  background: white;
  border-radius: 0;
  overflow: hidden;
  transition: box-shadow 0.3s ease;
}

.product-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.product-image-container {
  position: relative;
  aspect-ratio: 3/4;
  background-color: #f5f5f5;
  overflow: hidden;
}

.sale-badge {
  position: absolute;
  top: 12px;
  left: 12px;
  background: black;
  color: white;
  padding: 4px 8px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
}

.product-info {
  padding: 16px;
}

.brand-name {
  font-size: 0.875rem;
  color: #666;
  margin-bottom: 4px;
}

.product-name {
  font-size: 1rem;
  font-weight: 400;
  color: black;
  margin-bottom: 8px;
  line-height: 1.4;
}

.price-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.current-price {
  font-weight: 600;
  color: black;
}

.original-price {
  font-size: 0.875rem;
  color: #666;
  text-decoration: line-through;
}
