'use client'

import { useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { ChevronDownIcon, AdjustmentsHorizontalIcon } from '@heroicons/react/24/outline'

const products = [
  {
    id: 1,
    name: 'Wool overcoat',
    brand: 'The Row',
    price: '£2,400',
    image: '/products/the-row-coat.jpg',
    colors: ['Black', 'Navy', 'Camel']
  },
  {
    id: 2,
    name: 'Cashmere sweater',
    brand: '<PERSON><PERSON><PERSON> Cucinelli',
    price: '£1,200',
    image: '/products/brunello-mens-sweater.jpg',
    colors: ['Grey', 'Navy', 'Beige']
  },
  {
    id: 3,
    name: 'Cotton shirt',
    brand: 'Tom Ford',
    price: '£450',
    image: '/products/tom-ford-shirt.jpg',
    colors: ['White', 'Blue', 'Black']
  },
  {
    id: 4,
    name: 'Leather sneakers',
    brand: 'Common Projects',
    price: '£350',
    image: '/products/common-projects-sneakers.jpg',
    colors: ['White', 'Black']
  },
  {
    id: 5,
    name: 'Wool trousers',
    brand: 'Acne Studios',
    price: '£380',
    image: '/products/acne-trousers.jpg',
    colors: ['Black', 'Navy', 'Grey']
  },
  {
    id: 6,
    name: 'Leather jacket',
    brand: 'Saint Laurent',
    price: '£4,200',
    image: '/products/saint-laurent-mens-jacket.jpg',
    colors: ['Black']
  }
]

const filters = {
  designers: ['Acne Studios', 'Bottega Veneta', 'Brunello Cucinelli', 'Common Projects', 'Fear of God', 'Jacquemus', 'Kenzo', 'Off-White', 'Saint Laurent', 'Stone Island', 'The Row', 'Thom Browne', 'Tom Ford', 'Valentino'],
  categories: ['Clothing', 'Shoes', 'Bags', 'Accessories'],
  sizes: ['XS', 'S', 'M', 'L', 'XL', 'XXL'],
  colors: ['Black', 'White', 'Grey', 'Navy', 'Brown', 'Beige', 'Red', 'Blue', 'Green'],
  price: ['Under £100', '£100 - £250', '£250 - £500', '£500 - £1000', '£1000 - £2000', 'Over £2000']
}

export default function MenPage() {
  const [showFilters, setShowFilters] = useState(false)
  const [sortBy, setSortBy] = useState('newest')

  return (
    <div className="min-h-screen bg-white">
      {/* Breadcrumb */}
      <div className="border-b border-gray-200">
        <div className="container mx-auto px-4 py-4">
          <nav className="text-sm text-gray-500">
            <Link href="/" className="hover:text-black">Home</Link>
            <span className="mx-2">/</span>
            <span className="text-black">Men</span>
          </nav>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar Filters */}
          <aside className={`lg:w-1/4 ${showFilters ? 'block' : 'hidden lg:block'}`}>
            <div className="space-y-8">
              <div>
                <h3 className="font-medium text-black mb-4">DESIGNERS</h3>
                <div className="space-y-2 max-h-48 overflow-y-auto">
                  {filters.designers.map((designer) => (
                    <label key={designer} className="flex items-center text-sm">
                      <input type="checkbox" className="mr-2" />
                      {designer}
                    </label>
                  ))}
                </div>
              </div>

              <div>
                <h3 className="font-medium text-black mb-4">CATEGORY</h3>
                <div className="space-y-2">
                  {filters.categories.map((category) => (
                    <label key={category} className="flex items-center text-sm">
                      <input type="checkbox" className="mr-2" />
                      {category}
                    </label>
                  ))}
                </div>
              </div>

              <div>
                <h3 className="font-medium text-black mb-4">SIZE</h3>
                <div className="grid grid-cols-3 gap-2">
                  {filters.sizes.map((size) => (
                    <button
                      key={size}
                      className="border border-gray-300 py-2 text-sm hover:border-black transition-colors"
                    >
                      {size}
                    </button>
                  ))}
                </div>
              </div>

              <div>
                <h3 className="font-medium text-black mb-4">COLOR</h3>
                <div className="grid grid-cols-6 gap-2">
                  {filters.colors.map((color) => (
                    <button
                      key={color}
                      className="w-8 h-8 border border-gray-300 rounded-full"
                      style={{ backgroundColor: color.toLowerCase() }}
                      title={color}
                    />
                  ))}
                </div>
              </div>

              <div>
                <h3 className="font-medium text-black mb-4">PRICE</h3>
                <div className="space-y-2">
                  {filters.price.map((price) => (
                    <label key={price} className="flex items-center text-sm">
                      <input type="checkbox" className="mr-2" />
                      {price}
                    </label>
                  ))}
                </div>
              </div>
            </div>
          </aside>

          {/* Main Content */}
          <main className="lg:w-3/4">
            {/* Header */}
            <div className="flex items-center justify-between mb-8">
              <div>
                <h1 className="text-3xl font-light text-black mb-2">Men</h1>
                <p className="text-gray-600">{products.length} products</p>
              </div>
              
              <div className="flex items-center gap-4">
                <button
                  onClick={() => setShowFilters(!showFilters)}
                  className="lg:hidden flex items-center gap-2 text-sm font-medium"
                >
                  <AdjustmentsHorizontalIcon className="h-4 w-4" />
                  Filters
                </button>
                
                <div className="relative">
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="appearance-none bg-white border border-gray-300 px-4 py-2 pr-8 text-sm focus:outline-none focus:border-black"
                  >
                    <option value="newest">Newest</option>
                    <option value="price-low">Price: Low to High</option>
                    <option value="price-high">Price: High to Low</option>
                    <option value="popular">Most Popular</option>
                  </select>
                  <ChevronDownIcon className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 pointer-events-none" />
                </div>
              </div>
            </div>

            {/* Product Grid */}
            <div className="grid grid-cols-2 lg:grid-cols-3 gap-6">
              {products.map((product) => (
                <Link key={product.id} href={`/product/${product.id}`} className="group">
                  <div className="relative aspect-[3/4] mb-4 overflow-hidden bg-gray-100">
                    <Image
                      src={product.image}
                      alt={product.name}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs text-gray-500 uppercase tracking-wide">{product.brand}</p>
                    <h3 className="text-sm font-medium text-black line-clamp-2">{product.name}</h3>
                    <p className="text-sm font-medium text-black">{product.price}</p>
                    <div className="flex gap-1 mt-2">
                      {product.colors.map((color, index) => (
                        <div
                          key={index}
                          className="w-3 h-3 border border-gray-300 rounded-full"
                          style={{ backgroundColor: color.toLowerCase() }}
                          title={color}
                        />
                      ))}
                    </div>
                  </div>
                </Link>
              ))}
            </div>

            {/* Load More */}
            <div className="text-center mt-12">
              <button className="border border-black text-black px-8 py-3 text-sm font-medium hover:bg-black hover:text-white transition-colors">
                LOAD MORE
              </button>
            </div>
          </main>
        </div>
      </div>
    </div>
  )
}
