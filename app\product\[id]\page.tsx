
'use client'

import { useState } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { ChevronLeftIcon, ChevronRightIcon, HeartIcon } from '@heroicons/react/24/outline'
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid'

// Mock product data
const productData = {
  id: 1,
  brand: 'The Row',
  name: 'Moon oversized cotton-poplin shirt',
  price: '£1,200',
  images: [
    '/products/product-1.svg',
    '/products/product-2.svg',
    '/products/product-3.svg',
    '/products/product-4.svg'
  ],
  sizes: ['XS', 'S', 'M', 'L', 'XL'],
  description: `An elegant example of The Row's exacting tailoring, this Moon oversized shirt is cut from crisp cotton poplin. Its streamlined details and elevated design ensure this piece can be dressed up or down.`,
  details: [
    'Made from premium cotton poplin',
    'Oversized silhouette',
    'Button-front closure',
    'Long sleeves',
    'Made in Italy'
  ],
  care: 'Machine wash cold'
}

export default function ProductPage() {
  const [selectedSize, setSelectedSize] = useState('')
  const [selectedImage, setSelectedImage] = useState(0)
  const [isWishlisted, setIsWishlisted] = useState(false)

  const nextImage = () => {
    setSelectedImage((prev) => (prev + 1) % productData.images.length)
  }

  const prevImage = () => {
    setSelectedImage((prev) => (prev - 1 + productData.images.length) % productData.images.length)
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Breadcrumb */}
      <div className="max-w-7xl mx-auto px-4 py-4">
        <nav className="text-sm text-gray-500">
          <Link href="/" className="hover:text-black">Home</Link>
          <span className="mx-2">/</span>
          <Link href="/women" className="hover:text-black">Women</Link>
          <span className="mx-2">/</span>
          <span className="text-black">{productData.name}</span>
        </nav>
      </div>

      {/* Desktop and Mobile Product View */}
      <div className="max-w-7xl mx-auto px-4 pb-16">
        <div className="lg:grid lg:grid-cols-2 lg:gap-16">
          {/* Product Images */}
          <div className="lg:sticky lg:top-24 lg:h-fit">
            {/* Main Product Image */}
            <div className="aspect-[4/5] relative bg-gray-50 mb-4">
              <Image
                src={productData.images[selectedImage]}
                alt={productData.name}
                fill
                className="object-cover"
                priority
              />

              {/* Navigation arrows */}
              <button
                onClick={prevImage}
                className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white p-3 rounded-full shadow-lg transition-all"
              >
                <ChevronLeftIcon className="h-5 w-5" />
              </button>
              <button
                onClick={nextImage}
                className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white p-3 rounded-full shadow-lg transition-all"
              >
                <ChevronRightIcon className="h-5 w-5" />
              </button>
            </div>

            {/* Thumbnail Images */}
            <div className="hidden lg:grid lg:grid-cols-4 gap-2 mb-4">
              {productData.images.map((image, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedImage(index)}
                  className={`aspect-square relative bg-gray-50 border-2 transition-all ${
                    selectedImage === index ? 'border-black' : 'border-transparent hover:border-gray-300'
                  }`}
                >
                  <Image
                    src={image}
                    alt={`${productData.name} view ${index + 1}`}
                    fill
                    className="object-cover"
                  />
                </button>
              ))}
            </div>

            {/* Mobile Image dots */}
            <div className="flex justify-center gap-2 py-4 lg:hidden">
              {productData.images.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedImage(index)}
                  className={`w-2 h-2 rounded-full transition-all ${
                    selectedImage === index ? 'bg-black' : 'bg-gray-300'
                  }`}
                />
              ))}
            </div>
          </div>

          {/* Product Info */}
          <div className="lg:py-0 py-6">
            <div className="text-sm text-gray-500 mb-2 uppercase tracking-wide">{productData.brand}</div>
            <h1 className="text-2xl lg:text-3xl font-light mb-4 text-black">{productData.name}</h1>
            <div className="text-2xl font-semibold mb-8 text-black">{productData.price}</div>

            {/* Size Selection */}
            <div className="mb-8">
              <div className="flex items-center justify-between mb-4">
                <span className="text-sm font-medium">Select size</span>
                <Link href="#" className="text-sm text-gray-600 underline hover:text-black">Size Guide</Link>
              </div>

              {/* Size buttons for desktop */}
              <div className="hidden lg:grid lg:grid-cols-5 gap-2 mb-4">
                {productData.sizes.map((size) => (
                  <button
                    key={size}
                    onClick={() => setSelectedSize(size)}
                    className={`py-3 px-4 border text-sm font-medium transition-all ${
                      selectedSize === size
                        ? 'border-black bg-black text-white'
                        : 'border-gray-300 hover:border-black'
                    }`}
                  >
                    {size}
                  </button>
                ))}
              </div>

              {/* Size dropdown for mobile */}
              <select
                value={selectedSize}
                onChange={(e) => setSelectedSize(e.target.value)}
                className="w-full p-3 border border-gray-300 text-sm lg:hidden focus:outline-none focus:border-black"
              >
                <option value="">Select size</option>
                {productData.sizes.map((size) => (
                  <option key={size} value={size}>{size}</option>
                ))}
              </select>
            </div>

            {/* Action Buttons */}
            <div className="space-y-4 mb-8">
              <button
                disabled={!selectedSize}
                className={`w-full py-4 font-medium text-base transition-all ${
                  selectedSize
                    ? 'bg-black text-white hover:bg-gray-800'
                    : 'bg-gray-200 text-gray-400 cursor-not-allowed'
                }`}
              >
                {selectedSize ? 'Add to bag' : 'Select a size'}
              </button>
              <button
                onClick={() => setIsWishlisted(!isWishlisted)}
                className="w-full border-2 border-black py-4 flex items-center justify-center gap-2 font-medium hover:bg-black hover:text-white transition-all"
              >
                {isWishlisted ? (
                  <HeartSolidIcon className="h-5 w-5 text-red-500" />
                ) : (
                  <HeartIcon className="h-5 w-5" />
                )}
                {isWishlisted ? 'Remove from wishlist' : 'Add to wishlist'}
              </button>
            </div>

            {/* Product Details */}
            <div className="space-y-8">
              {/* Description */}
              <div>
                <h3 className="font-semibold mb-4 text-lg">Description</h3>
                <p className="text-gray-600 leading-relaxed">{productData.description}</p>
              </div>

              {/* Product Details */}
              <div>
                <h3 className="font-semibold mb-4 text-lg">Details</h3>
                <ul className="space-y-2">
                  {productData.details.map((detail, index) => (
                    <li key={index} className="text-gray-600 flex items-start">
                      <span className="w-2 h-2 bg-black rounded-full mt-2 mr-3 flex-shrink-0"></span>
                      {detail}
                    </li>
                  ))}
                </ul>
              </div>

              {/* Size and fit */}
              <div>
                <h3 className="font-semibold mb-4 text-lg">Size and fit</h3>
                <p className="text-gray-600">The model is 180cm tall and wears a size M</p>
              </div>

              {/* Care Instructions */}
              <div>
                <h3 className="font-semibold mb-4 text-lg">Care Instructions</h3>
                <p className="text-gray-600">{productData.care}</p>
              </div>

              {/* Delivery and Returns */}
              <div className="border-t border-gray-200 pt-6">
                <button className="flex items-center justify-between w-full py-4 text-left">
                  <span className="font-semibold text-lg">Delivery and Returns</span>
                  <ChevronRightIcon className="h-5 w-5 text-gray-400" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* You might also like */}
      <section className="max-w-7xl mx-auto px-4 py-16 border-t border-gray-200">
        <h3 className="text-2xl font-light mb-8 text-center">You might also like</h3>
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Mock related products */}
          {[1, 2, 3, 4].map((item) => (
            <Link key={item} href={`/product/${item}`} className="group">
              <div className="aspect-[3/4] relative bg-gray-50 mb-3 overflow-hidden">
                <Image
                  src={productData.images[(item - 1) % productData.images.length]}
                  alt={`Related product ${item}`}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                />
              </div>
              <div className="text-sm text-gray-500 mb-1 uppercase tracking-wide">{productData.brand}</div>
              <div className="text-sm text-black mb-2 line-clamp-2">Similar {productData.name}</div>
              <div className="text-sm font-semibold">{productData.price}</div>
            </Link>
          ))}
        </div>
      </section>
    </div>
  )
}
