"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/CategoryCarousel.tsx":
/*!*****************************************!*\
  !*** ./components/CategoryCarousel.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CategoryCarousel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst categories = [\n    {\n        id: 'women',\n        name: 'Women',\n        image: '/carousel/women.svg',\n        href: '/category/women'\n    },\n    {\n        id: 'men',\n        name: 'Men',\n        image: '/carousel/men.svg',\n        href: '/category/men'\n    },\n    {\n        id: 'bags',\n        name: 'Bags',\n        image: '/carousel/bags.svg',\n        href: '/category/bags'\n    },\n    {\n        id: 'shoes',\n        name: 'Shoes',\n        image: '/carousel/shoes.svg',\n        href: '/category/shoes'\n    }\n];\nfunction CategoryCarousel() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex gap-6 overflow-x-auto scrollbar-hide px-4 md:px-6 lg:px-8\",\n            children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    href: category.href,\n                    className: \"flex-none group\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-40 h-40 md:w-48 md:h-48 relative overflow-hidden rounded-lg bg-gray-100 transition-all duration-300 group-hover:scale-105 group-hover:shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    src: category.image,\n                                    alt: category.name,\n                                    fill: true,\n                                    className: \"object-cover transition-transform duration-300 group-hover:scale-110\",\n                                    sizes: \"(max-width: 768px) 160px, 192px\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\CategoryCarousel.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\CategoryCarousel.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-white text-lg font-semibold text-center opacity-0 group-hover:opacity-100 transition-opacity duration-300 transform translate-y-2 group-hover:translate-y-0\",\n                                        children: category.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\CategoryCarousel.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\CategoryCarousel.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\CategoryCarousel.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"mt-3 text-sm font-medium text-center text-gray-900 group-hover:text-black transition-colors\",\n                            children: category.name\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\CategoryCarousel.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, category.id, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\CategoryCarousel.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\CategoryCarousel.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\components\\\\CategoryCarousel.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n_c = CategoryCarousel;\nvar _c;\n$RefreshReg$(_c, \"CategoryCarousel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/CategoryCarousel.tsx\n"));

/***/ })

});