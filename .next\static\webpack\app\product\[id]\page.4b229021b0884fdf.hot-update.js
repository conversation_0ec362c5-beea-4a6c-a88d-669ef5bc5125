"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/product/[id]/page",{

/***/ "(app-pages-browser)/./app/product/[id]/page.tsx":
/*!***********************************!*\
  !*** ./app/product/[id]/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_HeartIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=HeartIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/HeartIcon.js\");\n/* harmony import */ var _barrel_optimize_names_HeartIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=HeartIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/HeartIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst product = {\n    id: 1,\n    name: 'Moon oversized cotton-poplin shirt',\n    brand: 'The Row',\n    price: '£1,200',\n    description: 'An elegant statement of The Row\\'s exacting, tailoring, this cream Moon shirt is cut from crisp cotton-poplin to an oversized fit with clean-lined details. Shown here with The Row Enzo straight-leg leather trousers, The Row Celine calf-hair-lined leather loafers and The Row Celine calf-hair-lined leather loafers.',\n    details: [\n        'Oversized fit',\n        'Button-front closure',\n        'Spread collar',\n        'Long sleeves with button cuffs',\n        'Curved hem',\n        'Made in Italy'\n    ],\n    sizeGuide: 'Size Guide',\n    deliveryReturns: 'Delivery and Returns',\n    images: [\n        '/products/the-row-shirt-1.jpg',\n        '/products/the-row-shirt-2.jpg',\n        '/products/the-row-shirt-3.jpg',\n        '/products/the-row-shirt-4.jpg',\n        '/products/the-row-shirt-5.jpg',\n        '/products/the-row-shirt-6.jpg'\n    ],\n    sizes: [\n        'XS',\n        'S',\n        'M',\n        'L',\n        'XL'\n    ],\n    colors: [\n        {\n            name: 'Cream',\n            value: '#F5F5DC'\n        },\n        {\n            name: 'White',\n            value: '#FFFFFF'\n        },\n        {\n            name: 'Black',\n            value: '#000000'\n        }\n    ]\n};\nconst recentlyViewed = [\n    {\n        id: 2,\n        name: 'Moon oversized cotton-poplin shirt',\n        brand: 'The Row',\n        price: '£1,200',\n        image: '/products/the-row-shirt-recent.jpg'\n    }\n];\nfunction ProductPage() {\n    _s();\n    const [selectedSize, setSelectedSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedColor, setSelectedColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(product.colors[0]);\n    const [selectedImage, setSelectedImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isWishlisted, setIsWishlisted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSizeGuide, setShowSizeGuide] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDelivery, setShowDelivery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDetails, setShowDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"text-sm text-gray-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/\",\n                                className: \"hover:text-black\",\n                                children: \"Home\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mx-2\",\n                                children: \"/\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/women\",\n                                className: \"hover:text-black\",\n                                children: \"Women\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mx-2\",\n                                children: \"/\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/women/designers\",\n                                className: \"hover:text-black\",\n                                children: \"Designers\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mx-2\",\n                                children: \"/\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-black\",\n                                children: \"The Row\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 pb-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"lg:grid lg:grid-cols-2 lg:gap-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:sticky lg:top-24 lg:h-fit\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"aspect-[4/5] relative bg-gray-50 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            src: productData.images[selectedImage],\n                                            alt: productData.name,\n                                            fill: true,\n                                            className: \"object-cover\",\n                                            priority: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: prevImage,\n                                            className: \"absolute left-4 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white p-3 rounded-full shadow-lg transition-all\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChevronLeftIcon, {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: nextImage,\n                                            className: \"absolute right-4 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white p-3 rounded-full shadow-lg transition-all\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChevronRightIcon, {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden lg:grid lg:grid-cols-4 gap-2 mb-4\",\n                                    children: productData.images.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setSelectedImage(index),\n                                            className: \"aspect-square relative bg-gray-50 border-2 transition-all \".concat(selectedImage === index ? 'border-black' : 'border-transparent hover:border-gray-300'),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                src: image,\n                                                alt: \"\".concat(productData.name, \" view \").concat(index + 1),\n                                                fill: true,\n                                                className: \"object-cover\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center gap-2 py-4 lg:hidden\",\n                                    children: productData.images.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setSelectedImage(index),\n                                            className: \"w-2 h-2 rounded-full transition-all \".concat(selectedImage === index ? 'bg-black' : 'bg-gray-300')\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:py-0 py-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-500 mb-2 uppercase tracking-wide\",\n                                    children: productData.brand\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl lg:text-3xl font-light mb-4 text-black\",\n                                    children: productData.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-semibold mb-8 text-black\",\n                                    children: productData.price\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Select size\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    href: \"#\",\n                                                    className: \"text-sm text-gray-600 underline hover:text-black\",\n                                                    children: \"Size Guide\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hidden lg:grid lg:grid-cols-5 gap-2 mb-4\",\n                                            children: productData.sizes.map((size)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setSelectedSize(size),\n                                                    className: \"py-3 px-4 border text-sm font-medium transition-all \".concat(selectedSize === size ? 'border-black bg-black text-white' : 'border-gray-300 hover:border-black'),\n                                                    children: size\n                                                }, size, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: selectedSize,\n                                            onChange: (e)=>setSelectedSize(e.target.value),\n                                            className: \"w-full p-3 border border-gray-300 text-sm lg:hidden focus:outline-none focus:border-black\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select size\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 17\n                                                }, this),\n                                                productData.sizes.map((size)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: size,\n                                                        children: size\n                                                    }, size, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4 mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            disabled: !selectedSize,\n                                            className: \"w-full py-4 font-medium text-base transition-all \".concat(selectedSize ? 'bg-black text-white hover:bg-gray-800' : 'bg-gray-200 text-gray-400 cursor-not-allowed'),\n                                            children: selectedSize ? 'Add to bag' : 'Select a size'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsWishlisted(!isWishlisted),\n                                            className: \"w-full border-2 border-black py-4 flex items-center justify-center gap-2 font-medium hover:bg-black hover:text-white transition-all\",\n                                            children: [\n                                                isWishlisted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HeartIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"h-5 w-5 text-red-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 19\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HeartIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 19\n                                                }, this),\n                                                isWishlisted ? 'Remove from wishlist' : 'Add to wishlist'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold mb-4 text-lg\",\n                                                    children: \"Description\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 leading-relaxed\",\n                                                    children: productData.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold mb-4 text-lg\",\n                                                    children: \"Details\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-2\",\n                                                    children: productData.details.map((detail, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"text-gray-600 flex items-start\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"w-2 h-2 bg-black rounded-full mt-2 mr-3 flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 224,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                detail\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 223,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold mb-4 text-lg\",\n                                                    children: \"Size and fit\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"The model is 180cm tall and wears a size M\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold mb-4 text-lg\",\n                                                    children: \"Care Instructions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: productData.care\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t border-gray-200 pt-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"flex items-center justify-between w-full py-4 text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold text-lg\",\n                                                        children: \"Delivery and Returns\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChevronRightIcon, {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"max-w-7xl mx-auto px-4 py-16 border-t border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-2xl font-light mb-8 text-center\",\n                        children: \"You might also like\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 lg:grid-cols-4 gap-6\",\n                        children: [\n                            1,\n                            2,\n                            3,\n                            4\n                        ].map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/product/\".concat(item),\n                                className: \"group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"aspect-[3/4] relative bg-gray-50 mb-3 overflow-hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            src: productData.images[(item - 1) % productData.images.length],\n                                            alt: \"Related product \".concat(item),\n                                            fill: true,\n                                            className: \"object-cover group-hover:scale-105 transition-transform duration-300\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500 mb-1 uppercase tracking-wide\",\n                                        children: productData.brand\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-black mb-2 line-clamp-2\",\n                                        children: [\n                                            \"Similar \",\n                                            productData.name\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-semibold\",\n                                        children: productData.price\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, item, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 258,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 256,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\matches-headless-ui\\\\matches-headless-ui\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductPage, \"BEYshFBWZKBHZq62VAoGV6CJ5zA=\");\n_c = ProductPage;\nvar _c;\n$RefreshReg$(_c, \"ProductPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/product/[id]/page.tsx\n"));

/***/ })

});