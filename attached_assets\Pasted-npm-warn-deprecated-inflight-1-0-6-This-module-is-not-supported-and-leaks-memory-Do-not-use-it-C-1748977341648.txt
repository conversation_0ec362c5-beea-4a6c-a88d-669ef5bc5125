npm warn deprecated inflight@1.0.6: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.
npm warn deprecated @humanwhocodes/config-array@0.11.14: Use @eslint/config-array instead
npm warn deprecated rimraf@3.0.2: Rimraf versions prior to v4 are no longer supported
npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
npm warn deprecated @supabase/auth-helpers-shared@0.6.3: This package is now deprecated - please use the @supabase/ssr package instead.
npm warn deprecated @humanwhocodes/object-schema@2.0.3: Use @eslint/object-schema instead
npm warn deprecated @supabase/auth-helpers-nextjs@0.8.7: This package is now deprecated - please use the @supabase/ssr package instead.
npm warn deprecated eslint@8.57.0: This version is no longer supported. Please see https://eslint.org/version-support for other options.

added 384 packages, and audited 385 packages in 31s

142 packages are looking for funding
  run `npm fund` for details

1 critical severity vulnerability

To address all issues, run:
  npm audit fix --force

Run `npm audit` for details.
PS C:\Users\<USER>\Downloads\matches-headless-ui\matches-headless-ui> npm run dev

> matches-headless@0.1.0 dev
> next dev

  ▲ Next.js 15.0.0
  - Local:        http://localhost:3000
  - Environments: .env

 ✓ Starting...
 ✓ Ready in 2.5s
 ○ Compiling / ...
Error: Cannot apply unknown utility class `px-4`. Are you using CSS modules or similar and missing `@reference`? https://tailwindcss.com/docs/functions-and-directives#reference-directive
    at onInvalidCandidate (C:\Users\<USER>\Downloads\matches-headless-ui\matches-headless-ui\node_modules\tailwindcss\dist\lib.js:18:1312)
    at ge (C:\Users\<USER>\Downloads\matches-headless-ui\matches-headless-ui\node_modules\tailwindcss\dist\lib.js:13:29803)
    at C:\Users\<USER>\Downloads\matches-headless-ui\matches-headless-ui\node_modules\tailwindcss\dist\lib.js:18:373
    at z (C:\Users\<USER>\Downloads\matches-headless-ui\matches-headless-ui\node_modules\tailwindcss\dist\lib.js:3:1656)
    at Le (C:\Users\<USER>\Downloads\matches-headless-ui\matches-headless-ui\node_modules\tailwindcss\dist\lib.js:18:172)
    at ki (C:\Users\<USER>\Downloads\matches-headless-ui\matches-headless-ui\node_modules\tailwindcss\dist\lib.js:35:780)
    at async bi (C:\Users\<USER>\Downloads\matches-headless-ui\matches-headless-ui\node_modules\tailwindcss\dist\lib.js:35:1123)
    at async _r (C:\Users\<USER>\Downloads\matches-headless-ui\matches-headless-ui\node_modules\@tailwindcss\node\dist\index.js:10:3384)
    at async p (C:\Users\<USER>\Downloads\matches-headless-ui\matches-headless-ui\node_modules\@tailwindcss\postcss\dist\index.js:10:4019)
    at async Object.Once (C:\Users\<USER>\Downloads\matches-headless-ui\matches-headless-ui\node_modules\@tailwindcss\postcss\dist\index.js:10:4290)
    at async LazyResult.runAsync (C:\Users\<USER>\Downloads\matches-headless-ui\matches-headless-ui\node_modules\next\node_modules\postcss\lib\lazy-result.js:261:11)
    at async Span.traceAsyncFn (C:\Users\<USER>\Downloads\matches-headless-ui\matches-headless-ui\node_modules\next\dist\trace\trace.js:157:20)
    at async C:\Users\<USER>\Downloads\matches-headless-ui\matches-headless-ui\node_modules\next\dist\build\webpack\loaders\postcss-loader\src\index.js:54:22
    at async Span.traceAsyncFn (C:\Users\<USER>\Downloads\matches-headless-ui\matches-headless-ui\node_modules\next\dist\trace\trace.js:157:20)