import Link from 'next/link'
import Image from 'next/image'

const designers = [
  { name: 'Acne Studios', image: '/designers/acne-studios.jpg', href: '/designers/acne-studios' },
  { name: 'Bottega Veneta', image: '/designers/bottega-veneta.jpg', href: '/designers/bottega-veneta' },
  { name: '<PERSON><PERSON><PERSON>', image: '/designers/brunello-cucinelli.jpg', href: '/designers/brunello-cucinelli' },
  { name: '<PERSON><PERSON><PERSON>', image: '/designers/ganni.jpg', href: '/designers/ganni' },
  { name: '<PERSON><PERSON><PERSON><PERSON>', image: '/designers/jacquemus.jpg', href: '/designers/jacquemus' },
  { name: '<PERSON><PERSON><PERSON>', image: '/designers/khaite.jpg', href: '/designers/khaite' },
  { name: '<PERSON><PERSON><PERSON>', image: '/designers/loewe.jpg', href: '/designers/loewe' },
  { name: '<PERSON>', image: '/designers/max-mara.jpg', href: '/designers/max-mara' },
  { name: 'P<PERSON>', image: '/designers/prada.jpg', href: '/designers/prada' },
  { name: 'Saint <PERSON>', image: '/designers/saint-laurent.jpg', href: '/designers/saint-laurent' },
  { name: 'The Row', image: '/designers/the-row.jpg', href: '/designers/the-row' },
  { name: 'Toteme', image: '/designers/toteme.jpg', href: '/designers/toteme' }
]

export default function DesignersPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Breadcrumb */}
      <div className="border-b border-gray-200">
        <div className="container mx-auto px-4 py-4">
          <nav className="text-sm text-gray-500">
            <Link href="/" className="hover:text-black">Home</Link>
            <span className="mx-2">/</span>
            <span className="text-black">Designers</span>
          </nav>
        </div>
      </div>

      {/* Hero Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl lg:text-5xl font-light text-black mb-6">DESIGNERS</h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Discover luxury fashion from the world's most prestigious designers. From established fashion houses to emerging talents.
          </p>
        </div>
      </section>

      {/* Designers Grid */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8">
            {designers.map((designer) => (
              <Link key={designer.name} href={designer.href} className="group">
                <div className="relative aspect-square mb-4 overflow-hidden bg-gray-100">
                  <Image
                    src={designer.image}
                    alt={designer.name}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <h3 className="text-sm font-medium text-center text-black group-hover:text-gray-600 transition-colors">
                  {designer.name}
                </h3>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Designer */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="relative aspect-[4/5]">
              <Image
                src="/featured-designer.jpg"
                alt="Featured Designer"
                fill
                className="object-cover"
              />
            </div>
            <div className="space-y-6">
              <h2 className="text-3xl font-light text-black">DESIGNER SPOTLIGHT</h2>
              <h3 className="text-xl font-medium text-black">The Row</h3>
              <p className="text-gray-600 leading-relaxed">
                Founded by Mary-Kate and Ashley Olsen, The Row is known for its minimalist aesthetic and impeccable craftsmanship. 
                Each piece is designed with a focus on exceptional materials and timeless silhouettes.
              </p>
              <Link 
                href="/designers/the-row" 
                className="inline-block bg-black text-white px-8 py-3 text-sm font-medium hover:bg-gray-800 transition-colors"
              >
                EXPLORE THE ROW
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* A-Z Index */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl font-light text-black text-center mb-12">Browse Designers A-Z</h2>
          
          <div className="grid grid-cols-6 md:grid-cols-13 gap-4 text-center">
            {Array.from({ length: 26 }, (_, i) => String.fromCharCode(65 + i)).map((letter) => (
              <Link 
                key={letter}
                href={`/designers?letter=${letter}`}
                className="py-3 border border-gray-300 text-sm font-medium hover:border-black hover:bg-black hover:text-white transition-colors"
              >
                {letter}
              </Link>
            ))}
          </div>
        </div>
      </section>
    </div>
  )
}
